package com.jxntv.gvideo.web.controller.be.model.vo.race;

import java.time.LocalDateTime;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 球赛活动VO - Web端管理
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RaceActivityVO", description = "球赛活动VO")
public class RaceActivityVO {

    @ApiModelProperty(value = "活动ID")
    private Long id;

    @ApiModelProperty(value = "活动主题", required = true)
    //@NotBlank(message = "活动主题不能为空")
    private String title;

    @ApiModelProperty(value = "活动简介")
    private String introduction;

    @ApiModelProperty(value = "活动开始时间", required = true)
    //@NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "活动结束时间", required = true)
    //@NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "活动状态：0-未开始、1-进行中、2-已结束")
    private Integer status;

    @ApiModelProperty(value = "活动状态文本")
    private String statusText;

    @ApiModelProperty(value = "活动配置信息")
    private RaceActivityConfigVO config;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateDate;
}
